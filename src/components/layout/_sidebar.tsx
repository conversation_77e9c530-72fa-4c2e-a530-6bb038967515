import { ROUTE_PATH } from "@enums/route-path";
import {
  faGear,
  faHouse,
  faListOl,
  faUserGroup,
} from "@fortawesome/free-solid-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { SidebarIcon } from "@phosphor-icons/react";
import { Link } from "@tanstack/react-router";
import { cn } from "@utils/cn";
import { useState } from "react";

const MENU_ITEMS = [
  {
    icon: faHouse,
    label: "Home",
    path: ROUTE_PATH.HOME,
  },
  {
    icon: faListOl,
    label: "Follow Up",
    path: ROUTE_PATH.FOLLOW_UP,
  },
  {
    icon: faUserGroup,
    label: "All Leads",
    path: ROUTE_PATH.ALL_LEADS,
  },
  {
    icon: faGear,
    label: "Settings",
    path: ROUTE_PATH.COLORS,
  },
];

export const Sidebar = () => {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div className={cn("sticky top-0 h-full", isExpanded ? "p-2" : "py-2")}>
      <div
        className={cn(
          "menu-vertical h-full items-end gap-2 border bg-base-100 p-2 shadow-sm",
          isExpanded
            ? "w-40 rounded-lg border-base-200"
            : "w-16 rounded-r-lg border-base-100",
          "transition-[width] duration-300"
        )}
      >
        <div className="mb-8 flex h-fit cursor-pointer items-center justify-center rounded-lg p-2 hover:bg-primary-content/30">
          <SidebarIcon size={24} onClick={toggleExpanded} />
        </div>

        {MENU_ITEMS.map(({ icon, label, path }) => (
          <Link
            key={path}
            to={path as string}
            className={cn(
              "flex w-full items-center rounded-lg p-3 text-xl hover:bg-primary-content/30",
              "[&.active]:bg-primary [&.active]:font-bold [&.active]:text-base-100",
              isExpanded ? "gap-3" : "justify-start"
            )}
          >
            <FontAwesomeIcon icon={icon} className="flex-shrink-0" />

            <span
              className={cn(
                "whitespace-nowrap font-medium text-sm transition-all duration-100 ease-in-out",
                isExpanded
                  ? "max-w-none translate-x-0 scale-100 opacity-100"
                  : "max-w-0 translate-x-4 scale-95 overflow-hidden opacity-0"
              )}
            >
              {label}
            </span>
          </Link>
        ))}
      </div>
    </div>
  );
};
