import type { IconProp } from "@fortawesome/fontawesome-svg-core";
import { faFacebook, faInstagram, faLine, faTiktok } from "@fortawesome/free-brands-svg-icons";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { cn } from "@utils/cn";

export type ChanneType = "facebook" | "line" | "instagram" | "tiktok";

interface ChannelBadgeConfig {
  type: ChanneType;
  icon: unknown;
  iconAlt: string;
  containerClasses: string;
}

const OPPORTUNITY_BADGE_CONFIG: Record<ChanneType, ChannelBadgeConfig> = {
  facebook: {
    containerClasses: "text-blue-500",
    icon: faFacebook,
    iconAlt: "Facebook",
    type: "facebook",
  },
  instagram: {
    containerClasses: "bg-pink-500 rounded-full text-base-100 p-1",
    icon: faInstagram,
    iconAlt: "Instagram",
    type: "instagram",
  },
  line: {
    containerClasses: "text-green-500",
    icon: faLine,
    iconAlt: "Line",
    type: "line",
  },
  tiktok: {
    containerClasses: "bg-black rounded-full text-base-100 p-1",
    icon: faTiktok,
    iconAlt: "Tiktok",
    type: "tiktok",
  },
};

const BASE_BADGE_CLASSES = "flex items-center aspect-square";

interface ChannelBadgeProps {
  type?: ChanneType;
  className?: string;
}

export const ChannelBadge = ({ type, className }: ChannelBadgeProps) => {
  if (!type) {
    return null;
  }

  const config = OPPORTUNITY_BADGE_CONFIG[type];

  return (
    <div className={cn(BASE_BADGE_CLASSES, config.containerClasses, className)}>
      <FontAwesomeIcon icon={config.icon as IconProp} />
    </div>
  );
};
